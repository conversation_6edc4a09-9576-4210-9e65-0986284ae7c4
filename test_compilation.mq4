//+------------------------------------------------------------------+
//|                                          test_compilation.mq4 |
//|                                Test compilation fixes |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Test Compilation"
#property version   "1.00"
#property strict
#property script_show_inputs

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
   Print("=== 测试编译修复 ===");
   
   // 测试1: 变量初始化修复
   Print("测试1: 变量初始化修复");
   
   int testDirection = 2;
   switch(testDirection)
   {
      case 0:
         Print("买入模式");
         break;
         
      case 1:
         Print("卖出模式");
         break;
         
      case 2:
      {
         // 使用大括号包围case内容，避免变量初始化跳过错误
         int orderCount = 5;
         Print("双向模式，订单数: ", orderCount);
         break;
      }
   }
   
   // 测试2: 时间类型修复
   Print("测试2: 时间类型修复");
   
   datetime lastUpdate = 0;
   datetime currentTime = TimeCurrent();
   
   if(currentTime - lastUpdate > 60)
   {
      Print("时间差检查正常");
      lastUpdate = currentTime;
   }
   
   // 测试3: ObjectsTotal函数修复
   Print("测试3: ObjectsTotal函数修复");
   
   int totalObjects = ObjectsTotal();
   Print("图表对象总数: ", totalObjects);
   
   // 测试清理对象的循环
   for(int i = ObjectsTotal() - 1; i >= 0; i--)
   {
      string objName = ObjectName(i);
      if(StringFind(objName, "Test_") == 0)
      {
         Print("找到测试对象: ", objName);
         // 这里可以删除对象
         // ObjectDelete(objName);
      }
   }
   
   // 测试4: 类型转换检查
   Print("测试4: 类型转换检查");
   
   double atr = 0.0015;
   double avgPrice = 1.2345;
   double volatility = (atr / avgPrice) * 100.0; // 明确使用double类型
   
   Print("波动率计算: ", DoubleToString(volatility, 4), "%");
   
   // 测试5: 函数调用检查
   Print("测试5: 函数调用检查");
   
   // 测试技术指标函数
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   double atrValue = iATR(Symbol(), PERIOD_CURRENT, 14, 0);
   
   if(ma20 > 0)
   {
      Print("MA20: ", DoubleToString(ma20, Digits));
   }
   
   if(atrValue > 0)
   {
      Print("ATR: ", DoubleToString(atrValue, Digits));
   }
   
   Print("=== 编译修复测试完成 ===");
   Print("结论: 所有语法错误已修复");
}
//+------------------------------------------------------------------+
