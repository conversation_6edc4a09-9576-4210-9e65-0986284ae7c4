//+------------------------------------------------------------------+
//|                                                  MartingaleEA.mq4 |
//|                                    Copyright 2024, Martingale EA |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, Martingale EA"
#property link      ""
#property version   "1.00"
#property strict

//+------------------------------------------------------------------+
//| 外部参数定义 (External Parameters Definition)                     |
//+------------------------------------------------------------------+
// 1. 初始手数参数
extern double Lots = 0.01;              // 初始交易手数

// 2. 加仓倍数参数
extern double Multiplier = 2.0;         // 加仓手数倍数

// 3. 最大订单数参数
extern int MaxOrders = 7;                // 最大同时持有订单数

// 4. 加仓间距参数
extern int GridStep = 30;                // 网格加仓间距(点数)

// 5. 止盈止损参数
extern int TakeProfit = 50;              // 止盈点数
extern int StopLoss = 200;               // 止损点数

// 6. 交易方向参数
extern int TradeDirection = 2;           // 交易方向: 0=仅买入, 1=仅卖出, 2=双向

// 7. 魔术号码参数
extern int MagicNumber = 123456;         // EA识别订单的魔术号码

// 日志系统参数
extern bool EnableFileLogging = true;    // 启用文件日志
extern bool EnableDebugMode = false;     // 启用调试模式
extern int LogLevel = 2;                 // 日志级别: 0=Error, 1=Info, 2=Debug

// 网格交易增强参数
extern bool EnableDynamicGrid = true;    // 启用动态网格
extern double VolatilityMultiplier = 1.5; // 波动率乘数
extern int MinGridStep = 15;             // 最小网格间距(点)
extern int MaxGridStep = 100;            // 最大网格间距(点)
extern bool EnableGridReset = true;      // 启用网格重置
extern double GridResetProfit = 50.0;    // 网格重置盈利阈值

// 用户界面参数
extern bool ShowInfoPanel = true;        // 显示信息面板
extern bool ShowGridLines = true;        // 显示网格线
extern bool ShowStatusInfo = true;       // 显示状态信息
extern int InfoPanelCorner = 0;          // 信息面板位置 (0=左上, 1=右上, 2=左下, 3=右下)
extern color InfoTextColor = clrWhite;   // 信息文字颜色
extern color ProfitColor = clrLime;       // 盈利颜色
extern color LossColor = clrRed;          // 亏损颜色
extern color GridLineColor = clrYellow;   // 网格线颜色

// 动态止损止盈参数
extern bool EnableTrailingStop = true;   // 启用移动止损
extern int TrailingStopDistance = 20;    // 移动止损距离(点)
extern bool EnablePartialTP = true;      // 启用分批止盈
extern double PartialTPPercent = 50.0;   // 分批止盈比例(%)
extern bool UseATRForSL = true;          // 使用ATR计算动态止损

//+------------------------------------------------------------------+
//| 全局变量声明区域 (Global Variables Declaration)                    |
//+------------------------------------------------------------------+
// EA运行状态变量
bool g_IsInitialized = false;
bool g_IsTradeAllowed = true;
datetime g_LastTickTime = 0;

// 交易统计变量
int g_TotalOrders = 0;
double g_TotalProfit = 0.0;
int g_CurrentGridLevel = 0;

// 错误处理变量
int g_LastError = 0;
string g_LastErrorMessage = "";

//+------------------------------------------------------------------+
//| 参数验证函数 (Parameter Validation Function)                      |
//+------------------------------------------------------------------+
bool ValidateParameters()
{
   // 8. 添加参数范围验证逻辑，确保参数值在合理范围内
   Print("=== 开始参数验证 ===");

   bool isValid = true;

   // 验证初始手数
   if(Lots < 0.01 || Lots > 100.0)
   {
      Print("错误: 初始手数超出范围 (0.01-100.0), 当前值: ", Lots);
      isValid = false;
   }

   // 验证加仓倍数
   if(Multiplier < 1.1 || Multiplier > 10.0)
   {
      Print("错误: 加仓倍数超出范围 (1.1-10.0), 当前值: ", Multiplier);
      isValid = false;
   }

   // 验证最大订单数
   if(MaxOrders < 1 || MaxOrders > 20)
   {
      Print("错误: 最大订单数超出范围 (1-20), 当前值: ", MaxOrders);
      isValid = false;
   }

   // 验证网格间距
   if(GridStep < 5 || GridStep > 1000)
   {
      Print("错误: 网格间距超出范围 (5-1000点), 当前值: ", GridStep);
      isValid = false;
   }

   // 验证止盈点数
   if(TakeProfit < 5 || TakeProfit > 1000)
   {
      Print("错误: 止盈点数超出范围 (5-1000点), 当前值: ", TakeProfit);
      isValid = false;
   }

   // 验证止损点数
   if(StopLoss < 10 || StopLoss > 2000)
   {
      Print("错误: 止损点数超出范围 (10-2000点), 当前值: ", StopLoss);
      isValid = false;
   }

   // 验证交易方向
   if(TradeDirection < 0 || TradeDirection > 2)
   {
      Print("错误: 交易方向参数无效 (0-2), 当前值: ", TradeDirection);
      isValid = false;
   }

   // 验证魔术号码
   if(MagicNumber < 1 || MagicNumber > 999999999)
   {
      Print("错误: 魔术号码超出范围 (1-999999999), 当前值: ", MagicNumber);
      isValid = false;
   }

   // 逻辑验证：止盈应该小于止损
   if(TakeProfit >= StopLoss)
   {
      Print("警告: 止盈点数应该小于止损点数，当前 TP=", TakeProfit, " SL=", StopLoss);
   }

   // 显示验证结果
   if(isValid)
   {
      Print("=== 参数验证通过 ===");
      Print("配置参数:");
      Print("- 初始手数: ", Lots);
      Print("- 加仓倍数: ", Multiplier);
      Print("- 最大订单数: ", MaxOrders);
      Print("- 网格间距: ", GridStep, " 点");
      Print("- 止盈: ", TakeProfit, " 点");
      Print("- 止损: ", StopLoss, " 点");
      Print("- 交易方向: ", TradeDirection, " (0=买入,1=卖出,2=双向)");
      Print("- 魔术号码: ", MagicNumber);
   }
   else
   {
      Print("=== 参数验证失败 ===");
   }

   return isValid;
}

//+------------------------------------------------------------------+
//| Expert initialization function (专家顾问初始化函数)                |
//+------------------------------------------------------------------+
int OnInit()
{
   // 1. 设置EA启动时的基本配置
   Print("=== Martingale EA 初始化开始 ===");

   // 初始化日志系统
   if(!InitializeLogging())
   {
      Print("警告: 日志系统初始化失败，继续运行");
   }

   // 首先验证外部参数
   if(!ValidateParameters())
   {
      LogError("参数验证失败，EA无法启动");
      return(INIT_PARAMETERS_INCORRECT);
   }

   // 检查交易权限
   if(!IsTradeAllowed())
   {
      LogError("交易未被允许，请检查EA设置");
      return(INIT_FAILED);
   }

   // 检查账户类型
   if(IsDemo())
   {
      LogInfo("当前为模拟账户");
   }
   else
   {
      LogInfo("当前为真实账户，请谨慎操作");
   }

   // 初始化全局变量
   g_IsInitialized = true;
   g_IsTradeAllowed = true;
   g_LastTickTime = TimeCurrent();
   g_TotalOrders = 0;
   g_TotalProfit = 0.0;
   g_CurrentGridLevel = 0;
   g_LastError = 0;
   g_LastErrorMessage = "";

   // 显示EA基本信息
   LogInfo("EA名称: Martingale Expert Advisor");
   LogInfo("版本: 1.00");
   LogInfo("账户号码: " + IntegerToString(AccountNumber()));
   LogInfo("账户余额: " + DoubleToString(AccountBalance(), 2));
   LogInfo("当前货币对: " + Symbol());
   LogInfo("当前时间框架: " + IntegerToString(Period()));

   LogInfo("=== Martingale EA 初始化完成 ===");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function (专家顾问反初始化函数)            |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // 2. 处理EA卸载时的清理工作
   LogInfo("=== Martingale EA 反初始化开始 ===");

   // 根据反初始化原因进行不同处理
   switch(reason)
   {
      case REASON_PROGRAM:
         LogInfo("反初始化原因: 程序终止");
         break;
      case REASON_REMOVE:
         LogInfo("反初始化原因: EA被移除");
         break;
      case REASON_RECOMPILE:
         LogInfo("反初始化原因: EA重新编译");
         break;
      case REASON_CHARTCHANGE:
         LogInfo("反初始化原因: 图表切换");
         break;
      case REASON_CHARTCLOSE:
         LogInfo("反初始化原因: 图表关闭");
         break;
      case REASON_PARAMETERS:
         LogInfo("反初始化原因: 参数修改");
         break;
      case REASON_ACCOUNT:
         LogInfo("反初始化原因: 账户切换");
         break;
      default:
         LogInfo("反初始化原因: 未知原因 (" + IntegerToString(reason) + ")");
         break;
   }

   // 清理全局变量
   g_IsInitialized = false;
   g_IsTradeAllowed = false;

   // 显示最终统计信息
   LogInfo("最终统计:");
   LogInfo("- 总订单数: " + IntegerToString(g_TotalOrders));
   LogInfo("- 总盈亏: " + DoubleToString(g_TotalProfit, 2));
   LogInfo("- 最大网格级别: " + IntegerToString(g_CurrentGridLevel));

   // 清理用户界面
   CleanupUI();

   LogInfo("=== Martingale EA 反初始化完成 ===");

   // 关闭日志系统
   CloseLogging();
}

//+------------------------------------------------------------------+
//| Expert tick function (专家顾问主要事件处理函数)                    |
//+------------------------------------------------------------------+
void OnTick()
{
   // 3. 处理每个价格变动

   // 检查EA是否已正确初始化
   if(!g_IsInitialized)
   {
      Print("错误: EA未正确初始化");
      return;
   }

   // 检查是否允许交易
   if(!IsTradeAllowed() || !g_IsTradeAllowed)
   {
      return;
   }

   // 更新最后Tick时间
   g_LastTickTime = TimeCurrent();

   // 获取当前市场信息
   double currentBid = Bid;
   double currentAsk = Ask;
   double currentSpread = Ask - Bid;

   // 基本市场检查
   if(currentBid <= 0 || currentAsk <= 0)
   {
      Print("错误: 无效的市场价格 Bid=", currentBid, " Ask=", currentAsk);
      return;
   }

   // 检查点差是否过大
   double maxSpread = 50 * Point; // 最大允许点差50点
   if(currentSpread > maxSpread)
   {
      Print("警告: 点差过大 (", currentSpread/Point, " 点), 暂停交易");
      return;
   }

   // 执行马丁格尔交易策略
   ExecuteMartingaleStrategy();

   // 执行动态止损止盈（每5个tick执行一次）
   static int slTpCount = 0;
   slTpCount++;
   if(slTpCount >= 5)
   {
      ExecuteDynamicSLTP();
      slTpCount = 0;
   }

   // 执行高级功能（每分钟执行一次）
   ExecuteAdvancedFeatures();

   // 更新用户界面（每10个tick更新一次以提高性能）
   static int tickCount = 0;
   tickCount++;
   if(tickCount >= 10)
   {
      UpdateUI();
      tickCount = 0;
   }

   // 更新错误状态
   int currentError = GetLastError();
   if(currentError != 0 && currentError != g_LastError)
   {
      g_LastError = currentError;
      g_LastErrorMessage = "错误代码: " + IntegerToString(currentError);
      Print("检测到新错误: ", g_LastErrorMessage);
   }
}

//+------------------------------------------------------------------+
//| 订单管理系统 (Order Management System)                            |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 创建OpenOrder()函数，实现新订单的开仓操作                        |
//+------------------------------------------------------------------+
int OpenOrder(int orderType, double lots, double price, double stopLoss, double takeProfit, string comment = "")
{
   int ticket = -1;
   int retries = 3;

   // 标准化价格
   price = NormalizeDouble(price, Digits);
   if(stopLoss > 0) stopLoss = NormalizeDouble(stopLoss, Digits);
   if(takeProfit > 0) takeProfit = NormalizeDouble(takeProfit, Digits);

   // 验证手数
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   if(lots < minLot || lots > maxLot)
   {
      Print("错误: 手数超出范围 Min=", minLot, " Max=", maxLot, " Current=", lots);
      return -1;
   }

   // 重试机制处理网络延迟
   for(int i = 0; i < retries; i++)
   {
      RefreshRates(); // 刷新价格

      ticket = OrderSend(Symbol(), orderType, lots, price, 3, stopLoss, takeProfit, comment, MagicNumber, 0, clrNONE);

      if(ticket > 0)
      {
         LogTrade("开仓", ticket, orderType, lots, price, "成功");
         g_TotalOrders++;
         return ticket;
      }
      else
      {
         int error = GetLastError();
         LogError("订单开仓失败 (尝试 " + IntegerToString(i+1) + "/" + IntegerToString(retries) + ")", error);

         // 处理特定错误
         if(error == ERR_TRADE_DISABLED || error == ERR_TRADE_NOT_ALLOWED)
         {
            LogError("交易被禁用，停止重试");
            break;
         }

         if(!AttemptErrorRecovery(error, "订单开仓"))
         {
            break;
         }
      }
   }

   return -1;
}

//+------------------------------------------------------------------+
//| 2. 创建CloseOrder()函数，实现订单的平仓操作                         |
//+------------------------------------------------------------------+
bool CloseOrder(int ticket, double lots = 0)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA Magic=", OrderMagicNumber(), " Expected=", MagicNumber);
      return false;
   }

   // 如果未指定手数，关闭全部
   if(lots <= 0) lots = OrderLots();

   double closePrice;
   color closeColor = clrRed;

   // 根据订单类型确定平仓价格
   if(OrderType() == OP_BUY)
   {
      closePrice = Bid;
      closeColor = clrBlue;
   }
   else if(OrderType() == OP_SELL)
   {
      closePrice = Ask;
      closeColor = clrRed;
   }
   else
   {
      Print("错误: 不支持的订单类型 ", OrderType());
      return false;
   }

   closePrice = NormalizeDouble(closePrice, Digits);

   // 重试机制
   for(int i = 0; i < 3; i++)
   {
      RefreshRates();

      if(OrderClose(ticket, lots, closePrice, 3, closeColor))
      {
         Print("订单平仓成功: Ticket=", ticket, " Lots=", lots, " Price=", closePrice);
         double profit = OrderProfit() + OrderSwap() + OrderCommission();
         g_TotalProfit += profit;
         return true;
      }
      else
      {
         int error = GetLastError();
         Print("订单平仓失败 (尝试 ", i+1, "/3): 错误=", error, " ", ErrorDescription(error));
         Sleep(1000);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 3. 创建ModifyOrder()函数，实现订单的修改操作                        |
//+------------------------------------------------------------------+
bool ModifyOrder(int ticket, double price, double stopLoss, double takeProfit)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   // 标准化价格
   price = NormalizeDouble(price, Digits);
   if(stopLoss > 0) stopLoss = NormalizeDouble(stopLoss, Digits);
   if(takeProfit > 0) takeProfit = NormalizeDouble(takeProfit, Digits);

   // 检查是否需要修改
   if(price == OrderOpenPrice() && stopLoss == OrderStopLoss() && takeProfit == OrderTakeProfit())
   {
      Print("订单无需修改: Ticket=", ticket);
      return true;
   }

   // 重试机制
   for(int i = 0; i < 3; i++)
   {
      if(OrderModify(ticket, price, stopLoss, takeProfit, 0, clrYellow))
      {
         Print("订单修改成功: Ticket=", ticket, " SL=", stopLoss, " TP=", takeProfit);
         return true;
      }
      else
      {
         int error = GetLastError();
         Print("订单修改失败 (尝试 ", i+1, "/3): 错误=", error, " ", ErrorDescription(error));
         Sleep(1000);
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 4. 创建GetOrderInfo()函数，实现订单信息的查询功能                   |
//+------------------------------------------------------------------+
bool GetOrderInfo(int ticket, double &openPrice, double &lots, int &orderType, double &profit, datetime &openTime)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   // 检查订单是否属于当前EA
   if(OrderMagicNumber() != MagicNumber)
   {
      return false;
   }

   // 获取订单信息
   openPrice = OrderOpenPrice();
   lots = OrderLots();
   orderType = OrderType();
   profit = OrderProfit() + OrderSwap() + OrderCommission();
   openTime = OrderOpenTime();

   return true;
}

//+------------------------------------------------------------------+
//| 5. 实现订单过滤机制，按魔术号码筛选EA相关订单                        |
//+------------------------------------------------------------------+
int CountOrdersByMagic(int magic = -1)
{
   if(magic == -1) magic = MagicNumber;

   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            count++;
         }
      }
   }
   return count;
}

//+------------------------------------------------------------------+
//| 获取指定魔术号码的所有订单票号                                       |
//+------------------------------------------------------------------+
int GetOrderTicketsByMagic(int &tickets[], int magic = -1)
{
   if(magic == -1) magic = MagicNumber;

   ArrayResize(tickets, 0);

   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magic)
         {
            int size = ArraySize(tickets);
            ArrayResize(tickets, size + 1);
            tickets[size] = OrderTicket();
         }
      }
   }

   return ArraySize(tickets);
}

//+------------------------------------------------------------------+
//| 6. 实现魔术号码管理，确保订单标识的唯一性                            |
//+------------------------------------------------------------------+
bool IsMagicNumberUnique(int magic)
{
   // 检查是否有其他EA使用相同的魔术号码
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderMagicNumber() == magic && OrderSymbol() != Symbol())
         {
            return false; // 发现其他货币对使用相同魔术号码
         }
      }
   }
   return true;
}

//+------------------------------------------------------------------+
//| 7. 添加订单执行错误处理，处理网络延迟、滑点等异常情况                 |
//+------------------------------------------------------------------+
string ErrorDescription(int errorCode)
{
   switch(errorCode)
   {
      case ERR_NO_ERROR: return "无错误";
      case ERR_NO_RESULT: return "无结果";
      case ERR_COMMON_ERROR: return "一般错误";
      case ERR_INVALID_TRADE_PARAMETERS: return "无效的交易参数";
      case ERR_SERVER_BUSY: return "服务器忙";
      case ERR_OLD_VERSION: return "版本过旧";
      case ERR_NO_CONNECTION: return "无连接";
      case ERR_NOT_ENOUGH_RIGHTS: return "权限不足";
      case ERR_TOO_FREQUENT_REQUESTS: return "请求过于频繁";
      case ERR_MALFUNCTIONAL_TRADE: return "交易功能故障";
      case ERR_ACCOUNT_DISABLED: return "账户被禁用";
      case ERR_INVALID_ACCOUNT: return "无效账户";
      case ERR_TRADE_TIMEOUT: return "交易超时";
      case ERR_INVALID_PRICE: return "无效价格";
      case ERR_INVALID_STOPS: return "无效止损";
      case ERR_INVALID_TRADE_VOLUME: return "无效交易量";
      case ERR_MARKET_CLOSED: return "市场关闭";
      case ERR_TRADE_DISABLED: return "交易被禁用";
      case ERR_NOT_ENOUGH_MONEY: return "资金不足";
      case ERR_PRICE_CHANGED: return "价格改变";
      case ERR_OFF_QUOTES: return "无报价";
      case ERR_BROKER_BUSY: return "经纪商忙";
      case ERR_REQUOTE: return "重新报价";
      case ERR_ORDER_LOCKED: return "订单锁定";
      case ERR_LONG_POSITIONS_ONLY_ALLOWED: return "只允许多头";
      case ERR_TOO_MANY_REQUESTS: return "请求过多";
      default: return "未知错误 (" + IntegerToString(errorCode) + ")";
   }
}

//+------------------------------------------------------------------+
//| 关闭所有EA相关订单                                                  |
//+------------------------------------------------------------------+
bool CloseAllOrders()
{
   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);
   bool success = true;

   Print("开始关闭所有订单，共 ", count, " 个");

   for(int i = 0; i < count; i++)
   {
      if(!CloseOrder(tickets[i]))
      {
         success = false;
      }
   }

   return success;
}

//+------------------------------------------------------------------+
//| 基础马丁格尔逻辑 (Basic Martingale Logic)                          |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 实现CheckEntry()函数，判断首次开仓的市场条件                     |
//+------------------------------------------------------------------+
bool CheckEntry()
{
   // 检查是否已有订单
   if(CountOrdersByMagic() > 0)
   {
      return false; // 已有订单，不开新仓
   }

   // 检查交易时间
   if(!IsTradeTime())
   {
      return false;
   }

   // 检查账户资金
   double freeMargin = AccountFreeMargin();
   double requiredMargin = MarketInfo(Symbol(), MODE_MARGINREQUIRED) * Lots;

   if(freeMargin < requiredMargin * 2) // 保留2倍保证金
   {
      Print("警告: 可用保证金不足 Free=", freeMargin, " Required=", requiredMargin);
      return false;
   }

   // 检查点差
   double spread = (Ask - Bid) / Point;
   if(spread > 50) // 点差超过50点
   {
      Print("警告: 点差过大 ", spread, " 点");
      return false;
   }

   // 简单的入场条件（可以根据需要添加更复杂的逻辑）
   // 这里使用基本的价格波动作为入场信号
   static double lastPrice = 0;
   double currentPrice = (Bid + Ask) / 2;

   if(lastPrice == 0)
   {
      lastPrice = currentPrice;
      return false;
   }

   double priceChange = MathAbs(currentPrice - lastPrice);
   lastPrice = currentPrice;

   // 如果价格变动超过网格间距，可以考虑入场
   if(priceChange >= GridStep * Point)
   {
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 2. 实现CalculateLotSize()函数，根据加仓倍数计算新订单手数           |
//+------------------------------------------------------------------+
double CalculateLotSize(int level)
{
   double lotSize = Lots;

   // 根据加仓级别计算手数
   for(int i = 0; i < level; i++)
   {
      lotSize *= Multiplier;
   }

   // 检查手数限制
   double minLot = MarketInfo(Symbol(), MODE_MINLOT);
   double maxLot = MarketInfo(Symbol(), MODE_MAXLOT);
   double lotStep = MarketInfo(Symbol(), MODE_LOTSTEP);

   // 标准化手数
   lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

   // 限制在允许范围内
   if(lotSize < minLot) lotSize = minLot;
   if(lotSize > maxLot) lotSize = maxLot;

   Print("计算手数: Level=", level, " LotSize=", lotSize);

   return lotSize;
}

//+------------------------------------------------------------------+
//| 3. 实现CheckGrid()函数，判断网格加仓的时机和条件                    |
//+------------------------------------------------------------------+
bool CheckGrid(int &gridLevel, int &orderType, double &entryPrice)
{
   int tickets[];
   int orderCount = GetOrderTicketsByMagic(tickets);

   if(orderCount == 0)
   {
      return false; // 没有订单，不需要加仓
   }

   if(orderCount >= MaxOrders)
   {
      Print("已达到最大订单数限制: ", MaxOrders);
      return false;
   }

   // 找到最后一个订单
   int lastTicket = -1;
   datetime lastTime = 0;

   for(int i = 0; i < orderCount; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         if(OrderOpenTime() > lastTime)
         {
            lastTime = OrderOpenTime();
            lastTicket = OrderTicket();
         }
      }
   }

   if(lastTicket == -1)
   {
      return false;
   }

   // 获取最后订单信息
   if(!OrderSelect(lastTicket, SELECT_BY_TICKET))
   {
      return false;
   }

   double lastPrice = OrderOpenPrice();
   int lastType = OrderType();
   double currentPrice = (lastType == OP_BUY) ? Bid : Ask;

   // 计算价格距离
   double distance = MathAbs(currentPrice - lastPrice) / Point;

   // 检查是否达到加仓条件
   if(distance >= GridStep)
   {
      gridLevel = orderCount; // 当前级别

      // 确定加仓方向
      if(lastType == OP_BUY && currentPrice < lastPrice)
      {
         // 买单亏损，继续买入
         orderType = OP_BUY;
         entryPrice = Ask;
         return true;
      }
      else if(lastType == OP_SELL && currentPrice > lastPrice)
      {
         // 卖单亏损，继续卖出
         orderType = OP_SELL;
         entryPrice = Bid;
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 7. 实现加仓级别跟踪，记录当前加仓次数和级别                          |
//+------------------------------------------------------------------+
void UpdateGridLevel()
{
   int orderCount = CountOrdersByMagic();
   g_CurrentGridLevel = orderCount;

   if(orderCount > 0)
   {
      Print("当前网格级别: ", g_CurrentGridLevel, "/", MaxOrders);
   }
}

//+------------------------------------------------------------------+
//| 4. 支持买入模式(TradeDirection=0)的马丁格尔策略                     |
//+------------------------------------------------------------------+
bool ExecuteBuyStrategy()
{
   if(TradeDirection != 0 && TradeDirection != 2)
   {
      return false; // 不允许买入
   }

   // 检查首次开仓条件
   if(CountOrdersByMagic() == 0)
   {
      if(CheckEntry())
      {
         double lotSize = CalculateLotSize(0);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消买入操作");
            return false;
         }

         int ticket = OpenOrder(OP_BUY, lotSize, Ask, 0, 0, "Martingale Buy L0");
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }
   else
   {
      // 检查加仓条件
      int gridLevel, orderType;
      double entryPrice;

      if(EnhancedCheckGrid(gridLevel, orderType, entryPrice) && orderType == OP_BUY)
      {
         double lotSize = CalculateLotSize(gridLevel);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消加仓买入操作");
            return false;
         }

         string comment = "Martingale Buy L" + IntegerToString(gridLevel);
         int ticket = OpenOrder(OP_BUY, lotSize, entryPrice, 0, 0, comment);
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 5. 支持卖出模式(TradeDirection=1)的马丁格尔策略                     |
//+------------------------------------------------------------------+
bool ExecuteSellStrategy()
{
   if(TradeDirection != 1 && TradeDirection != 2)
   {
      return false; // 不允许卖出
   }

   // 检查首次开仓条件
   if(CountOrdersByMagic() == 0)
   {
      if(CheckEntry())
      {
         double lotSize = CalculateLotSize(0);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消卖出操作");
            return false;
         }

         int ticket = OpenOrder(OP_SELL, lotSize, Bid, 0, 0, "Martingale Sell L0");
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }
   else
   {
      // 检查加仓条件
      int gridLevel, orderType;
      double entryPrice;

      if(EnhancedCheckGrid(gridLevel, orderType, entryPrice) && orderType == OP_SELL)
      {
         double lotSize = CalculateLotSize(gridLevel);

         // 风险检查
         if(!ComprehensiveRiskCheck(lotSize))
         {
            Print("风险检查失败，取消加仓卖出操作");
            return false;
         }

         string comment = "Martingale Sell L" + IntegerToString(gridLevel);
         int ticket = OpenOrder(OP_SELL, lotSize, entryPrice, 0, 0, comment);
         if(ticket > 0)
         {
            // 设置止损止盈
            SetOrderStopLossAndTakeProfit(ticket);
            UpdateGridLevel();
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 6. 支持双向交易模式(TradeDirection=2)，同时处理买入和卖出           |
//+------------------------------------------------------------------+
void ExecuteMartingaleStrategy()
{
   // 更新网格级别
   UpdateGridLevel();

   switch(TradeDirection)
   {
      case 0: // 仅买入
         ExecuteBuyStrategy();
         break;

      case 1: // 仅卖出
         ExecuteSellStrategy();
         break;

      case 2: // 双向交易
      {
         // 在双向模式下，优先处理现有方向的加仓
         int orderCount = CountOrdersByMagic();
         if(orderCount == 0)
         {
            // 没有订单时，根据市场条件选择方向
            if(CheckEntry())
            {
               // 简单的方向选择逻辑（可以改进）
               static int lastDirection = OP_BUY;
               lastDirection = (lastDirection == OP_BUY) ? OP_SELL : OP_BUY;

               if(lastDirection == OP_BUY)
               {
                  ExecuteBuyStrategy();
               }
               else
               {
                  ExecuteSellStrategy();
               }
            }
         }
         else
         {
            // 有订单时，检查加仓条件
            ExecuteBuyStrategy();
            ExecuteSellStrategy();
         }
         break;
      }
   }
}

//+------------------------------------------------------------------+
//| 风险控制模块 (Risk Control Module)                                |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 实现CheckRisk()函数，评估当前持仓的风险水平                      |
//+------------------------------------------------------------------+
bool CheckRisk()
{
   Print("=== 开始风险评估 ===");

   bool riskAcceptable = true;

   // 检查账户基本信息
   double balance = AccountBalance();
   double equity = AccountEquity();
   double freeMargin = AccountFreeMargin();
   double usedMargin = AccountMargin();

   Print("账户信息:");
   Print("- 余额: ", balance);
   Print("- 净值: ", equity);
   Print("- 可用保证金: ", freeMargin);
   Print("- 已用保证金: ", usedMargin);

   // 1. 检查净值与余额比例（回撤检查）
   double drawdownPercent = 0;
   if(balance > 0)
   {
      drawdownPercent = ((balance - equity) / balance) * 100;
   }

   Print("当前回撤: ", drawdownPercent, "%");

   if(drawdownPercent > 30) // 回撤超过30%
   {
      Print("警告: 回撤过大 ", drawdownPercent, "%");
      riskAcceptable = false;
   }

   // 2. 检查保证金水平
   double marginLevel = 0;
   if(usedMargin > 0)
   {
      marginLevel = (equity / usedMargin) * 100;
   }

   Print("保证金水平: ", marginLevel, "%");

   if(marginLevel < 200 && marginLevel > 0) // 保证金水平低于200%
   {
      Print("警告: 保证金水平过低 ", marginLevel, "%");
      riskAcceptable = false;
   }

   // 3. 检查当前订单数量
   int orderCount = CountOrdersByMagic();
   Print("当前订单数: ", orderCount, "/", MaxOrders);

   if(orderCount >= MaxOrders)
   {
      Print("警告: 已达到最大订单数限制");
      riskAcceptable = false;
   }

   // 4. 检查总浮动盈亏
   double totalProfit = CalculateTotalProfit();
   double profitPercent = 0;
   if(balance > 0)
   {
      profitPercent = (totalProfit / balance) * 100;
   }

   Print("总浮动盈亏: ", totalProfit, " (", profitPercent, "%)");

   if(profitPercent < -20) // 浮亏超过20%
   {
      Print("警告: 浮动亏损过大 ", profitPercent, "%");
   }

   Print("风险评估结果: ", riskAcceptable ? "可接受" : "风险过高");

   return riskAcceptable;
}

//+------------------------------------------------------------------+
//| 计算总浮动盈亏                                                      |
//+------------------------------------------------------------------+
double CalculateTotalProfit()
{
   double totalProfit = 0;
   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
      }
   }

   return totalProfit;
}

//+------------------------------------------------------------------+
//| 2. 实现SetStopLoss()函数，为订单设置止损价格                       |
//+------------------------------------------------------------------+
bool SetStopLoss(int ticket, double stopLossPrice)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   double currentSL = OrderStopLoss();
   double currentTP = OrderTakeProfit();
   double openPrice = OrderOpenPrice();
   int orderType = OrderType();

   // 标准化止损价格
   stopLossPrice = NormalizeDouble(stopLossPrice, Digits);

   // 验证止损价格的合理性
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

   if(orderType == OP_BUY)
   {
      if(stopLossPrice >= Bid - minDistance)
      {
         Print("错误: 买单止损价格过高 SL=", stopLossPrice, " Bid=", Bid);
         return false;
      }
   }
   else if(orderType == OP_SELL)
   {
      if(stopLossPrice <= Ask + minDistance)
      {
         Print("错误: 卖单止损价格过低 SL=", stopLossPrice, " Ask=", Ask);
         return false;
      }
   }

   // 如果止损价格没有变化，不需要修改
   if(MathAbs(stopLossPrice - currentSL) < Point)
   {
      return true;
   }

   // 修改订单
   if(ModifyOrder(ticket, openPrice, stopLossPrice, currentTP))
   {
      Print("止损设置成功: Ticket=", ticket, " SL=", stopLossPrice);
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 3. 实现SetTakeProfit()函数，为订单设置止盈价格                     |
//+------------------------------------------------------------------+
bool SetTakeProfit(int ticket, double takeProfitPrice)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      Print("错误: 无法选择订单 ", ticket);
      return false;
   }

   if(OrderMagicNumber() != MagicNumber)
   {
      Print("错误: 订单不属于当前EA");
      return false;
   }

   double currentSL = OrderStopLoss();
   double currentTP = OrderTakeProfit();
   double openPrice = OrderOpenPrice();
   int orderType = OrderType();

   // 标准化止盈价格
   takeProfitPrice = NormalizeDouble(takeProfitPrice, Digits);

   // 验证止盈价格的合理性
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;

   if(orderType == OP_BUY)
   {
      if(takeProfitPrice <= Ask + minDistance)
      {
         Print("错误: 买单止盈价格过低 TP=", takeProfitPrice, " Ask=", Ask);
         return false;
      }
   }
   else if(orderType == OP_SELL)
   {
      if(takeProfitPrice >= Bid - minDistance)
      {
         Print("错误: 卖单止盈价格过高 TP=", takeProfitPrice, " Bid=", Bid);
         return false;
      }
   }

   // 如果止盈价格没有变化，不需要修改
   if(MathAbs(takeProfitPrice - currentTP) < Point)
   {
      return true;
   }

   // 修改订单
   if(ModifyOrder(ticket, openPrice, currentSL, takeProfitPrice))
   {
      Print("止盈设置成功: Ticket=", ticket, " TP=", takeProfitPrice);
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 4. 实现MaxOrdersCheck()函数，检查是否超过最大订单数限制             |
//+------------------------------------------------------------------+
bool MaxOrdersCheck()
{
   int currentOrders = CountOrdersByMagic();

   if(currentOrders >= MaxOrders)
   {
      Print("已达到最大订单数限制: ", currentOrders, "/", MaxOrders);
      return false;
   }

   Print("订单数检查通过: ", currentOrders, "/", MaxOrders);
   return true;
}

//+------------------------------------------------------------------+
//| 5. 添加紧急平仓功能，在极端情况下强制关闭所有订单                    |
//+------------------------------------------------------------------+
bool EmergencyCloseAll(string reason = "Emergency")
{
   Print("=== 紧急平仓触发 ===");
   Print("原因: ", reason);

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   if(count == 0)
   {
      Print("没有需要关闭的订单");
      return true;
   }

   Print("开始紧急关闭 ", count, " 个订单");

   bool allClosed = true;
   int closedCount = 0;

   for(int i = 0; i < count; i++)
   {
      if(CloseOrder(tickets[i]))
      {
         closedCount++;
         Print("订单 ", tickets[i], " 关闭成功");
      }
      else
      {
         allClosed = false;
         Print("订单 ", tickets[i], " 关闭失败");
      }

      Sleep(100); // 短暂延迟避免过于频繁的请求
   }

   Print("紧急平仓完成: ", closedCount, "/", count, " 个订单已关闭");

   // 禁用EA交易
   if(!allClosed)
   {
      g_IsTradeAllowed = false;
      Print("部分订单关闭失败，已禁用EA交易");
   }

   return allClosed;
}

//+------------------------------------------------------------------+
//| 6. 实现资金保护机制，防止账户资金过度损失                            |
//+------------------------------------------------------------------+
bool FundProtectionCheck()
{
   double balance = AccountBalance();
   double equity = AccountEquity();

   if(balance <= 0)
   {
      Print("错误: 账户余额异常");
      return false;
   }

   // 计算当前回撤百分比
   double drawdownPercent = ((balance - equity) / balance) * 100;

   // 设置最大允许回撤（可以通过外部参数配置）
   double maxDrawdownPercent = 50.0; // 最大回撤50%

   if(drawdownPercent >= maxDrawdownPercent)
   {
      Print("资金保护触发: 回撤 ", drawdownPercent, "% >= ", maxDrawdownPercent, "%");
      EmergencyCloseAll("资金保护 - 回撤过大");
      return false;
   }

   // 检查最小账户净值
   double minEquityPercent = 30.0; // 最小净值为余额的30%
   double minEquity = balance * (minEquityPercent / 100);

   if(equity <= minEquity)
   {
      Print("资金保护触发: 净值 ", equity, " <= 最小净值 ", minEquity);
      EmergencyCloseAll("资金保护 - 净值过低");
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 7. 添加保证金检查，确保有足够保证金支持新订单                        |
//+------------------------------------------------------------------+
bool MarginCheck(double lots)
{
   double freeMargin = AccountFreeMargin();
   double requiredMargin = MarketInfo(Symbol(), MODE_MARGINREQUIRED) * lots;

   // 保留安全边际（2倍保证金）
   double safetyMargin = requiredMargin * 2;

   if(freeMargin < safetyMargin)
   {
      Print("保证金不足: 可用=", freeMargin, " 需要=", safetyMargin, " (含安全边际)");
      return false;
   }

   // 检查保证金水平
   double usedMargin = AccountMargin();
   double equity = AccountEquity();

   if(usedMargin > 0)
   {
      double marginLevel = (equity / (usedMargin + requiredMargin)) * 100;

      if(marginLevel < 150) // 保证金水平低于150%
      {
         Print("保证金水平过低: ", marginLevel, "%");
         return false;
      }
   }

   Print("保证金检查通过: 可用=", freeMargin, " 需要=", requiredMargin);
   return true;
}

//+------------------------------------------------------------------+
//| 综合风险检查函数                                                    |
//+------------------------------------------------------------------+
bool ComprehensiveRiskCheck(double lots = 0)
{
   // 1. 基础风险检查
   if(!CheckRisk())
   {
      return false;
   }

   // 2. 资金保护检查
   if(!FundProtectionCheck())
   {
      return false;
   }

   // 3. 最大订单数检查
   if(!MaxOrdersCheck())
   {
      return false;
   }

   // 4. 保证金检查（如果指定了手数）
   if(lots > 0 && !MarginCheck(lots))
   {
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 设置订单的止损止盈                                                  |
//+------------------------------------------------------------------+
bool SetOrderStopLossAndTakeProfit(int ticket)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      return false;
   }

   double openPrice = OrderOpenPrice();
   int orderType = OrderType();
   double stopLoss = 0;
   double takeProfit = 0;

   // 计算止损止盈价格
   if(orderType == OP_BUY)
   {
      if(StopLoss > 0)
         stopLoss = openPrice - StopLoss * Point;
      if(TakeProfit > 0)
         takeProfit = openPrice + TakeProfit * Point;
   }
   else if(orderType == OP_SELL)
   {
      if(StopLoss > 0)
         stopLoss = openPrice + StopLoss * Point;
      if(TakeProfit > 0)
         takeProfit = openPrice - TakeProfit * Point;
   }

   bool success = true;

   // 设置止损
   if(stopLoss > 0 && !SetStopLoss(ticket, stopLoss))
   {
      success = false;
   }

   // 设置止盈
   if(takeProfit > 0 && !SetTakeProfit(ticket, takeProfit))
   {
      success = false;
   }

   return success;
}

//+------------------------------------------------------------------+
//| 日志和错误处理系统 (Logging and Error Handling System)            |
//+------------------------------------------------------------------+

// 日志级别常量
#define LOG_LEVEL_ERROR   0
#define LOG_LEVEL_INFO    1
#define LOG_LEVEL_DEBUG   2

// 全局日志变量
string g_LogFileName = "";
int g_LogFileHandle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| 初始化日志系统                                                      |
//+------------------------------------------------------------------+
bool InitializeLogging()
{
   if(!EnableFileLogging)
   {
      return true;
   }

   // 创建日志文件名（包含日期和EA名称）
   datetime currentTime = TimeCurrent();
   string dateStr = TimeToString(currentTime, TIME_DATE);
   StringReplace(dateStr, ".", "_");

   g_LogFileName = "MartingaleEA_" + dateStr + ".log";

   // 尝试打开日志文件
   g_LogFileHandle = FileOpen(g_LogFileName, FILE_WRITE | FILE_TXT | FILE_SHARE_READ);

   if(g_LogFileHandle == INVALID_HANDLE)
   {
      Print("警告: 无法创建日志文件 ", g_LogFileName);
      return false;
   }

   // 写入日志头信息
   string header = "=== Martingale EA 日志开始 ===\n";
   header += "时间: " + TimeToString(currentTime) + "\n";
   header += "账户: " + IntegerToString(AccountNumber()) + "\n";
   header += "货币对: " + Symbol() + "\n";
   header += "================================\n";

   FileWrite(g_LogFileHandle, header);
   FileFlush(g_LogFileHandle);

   LogInfo("日志系统初始化完成");
   return true;
}

//+------------------------------------------------------------------+
//| 关闭日志系统                                                        |
//+------------------------------------------------------------------+
void CloseLogging()
{
   if(g_LogFileHandle != INVALID_HANDLE)
   {
      LogInfo("日志系统关闭");

      string footer = "\n=== Martingale EA 日志结束 ===\n";
      footer += "时间: " + TimeToString(TimeCurrent()) + "\n";
      footer += "================================\n";

      FileWrite(g_LogFileHandle, footer);
      FileClose(g_LogFileHandle);
      g_LogFileHandle = INVALID_HANDLE;
   }
}

//+------------------------------------------------------------------+
//| 1. 创建LogInfo()函数，记录一般信息和交易操作日志                     |
//+------------------------------------------------------------------+
void LogInfo(string message)
{
   if(LogLevel < LOG_LEVEL_INFO)
   {
      return;
   }

   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [INFO] " + message;

   // 5. 实现Expert标签页日志，在MT4界面显示实时日志
   Print(logMessage);

   // 4. 实现文件日志功能，将日志信息保存到本地文件
   if(EnableFileLogging && g_LogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_LogFileHandle, logMessage);
      FileFlush(g_LogFileHandle);
   }
}

//+------------------------------------------------------------------+
//| 2. 创建LogError()函数，记录错误信息和异常情况                       |
//+------------------------------------------------------------------+
void LogError(string message, int errorCode = 0)
{
   if(LogLevel < LOG_LEVEL_ERROR)
   {
      return;
   }

   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [ERROR] " + message;

   if(errorCode != 0)
   {
      logMessage += " (错误代码: " + IntegerToString(errorCode) + " - " + ErrorDescription(errorCode) + ")";
   }

   // Expert标签页显示
   Print(logMessage);

   // 文件日志
   if(EnableFileLogging && g_LogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_LogFileHandle, logMessage);
      FileFlush(g_LogFileHandle);
   }

   // 更新全局错误状态
   g_LastError = errorCode;
   g_LastErrorMessage = message;
}

//+------------------------------------------------------------------+
//| 3. 创建LogDebug()函数，记录调试信息用于开发测试                     |
//+------------------------------------------------------------------+
void LogDebug(string message)
{
   if(LogLevel < LOG_LEVEL_DEBUG || !EnableDebugMode)
   {
      return;
   }

   string timestamp = TimeToString(TimeCurrent(), TIME_DATE | TIME_SECONDS);
   string logMessage = "[" + timestamp + "] [DEBUG] " + message;

   // Expert标签页显示
   Print(logMessage);

   // 文件日志
   if(EnableFileLogging && g_LogFileHandle != INVALID_HANDLE)
   {
      FileWrite(g_LogFileHandle, logMessage);
      FileFlush(g_LogFileHandle);
   }
}

//+------------------------------------------------------------------+
//| 记录交易操作日志                                                    |
//+------------------------------------------------------------------+
void LogTrade(string operation, int ticket, int orderType, double lots, double price, string result)
{
   string typeStr = (orderType == OP_BUY) ? "BUY" : "SELL";
   string message = "交易操作: " + operation + " | Ticket=" + IntegerToString(ticket) +
                   " | Type=" + typeStr + " | Lots=" + DoubleToString(lots, 2) +
                   " | Price=" + DoubleToString(price, Digits) + " | 结果=" + result;

   LogInfo(message);
}

//+------------------------------------------------------------------+
//| 6. 添加错误代码处理机制，识别和分类不同类型的错误                    |
//+------------------------------------------------------------------+
string ClassifyError(int errorCode)
{
   if(errorCode == ERR_NO_ERROR)
      return "正常";
   else if(errorCode >= 4000 && errorCode < 5000)
      return "运行时错误";
   else if(errorCode >= 4100 && errorCode < 4200)
      return "文件操作错误";
   else if(errorCode >= 4200 && errorCode < 4300)
      return "数组错误";
   else if(errorCode >= 4050 && errorCode < 4100)
      return "字符串错误";
   else if(errorCode >= 1 && errorCode < 100)
      return "交易错误";
   else if(errorCode >= 130 && errorCode < 150)
      return "价格错误";
   else
      return "未知错误类型";
}

//+------------------------------------------------------------------+
//| 处理和记录错误                                                      |
//+------------------------------------------------------------------+
bool HandleError(string context, int errorCode = 0)
{
   if(errorCode == 0)
   {
      errorCode = GetLastError();
   }

   if(errorCode == ERR_NO_ERROR)
   {
      return true;
   }

   string errorType = ClassifyError(errorCode);
   string message = context + " | 错误类型: " + errorType;

   LogError(message, errorCode);

   // 7. 实现异常恢复机制，在错误发生后自动恢复EA运行
   return AttemptErrorRecovery(errorCode, context);
}

//+------------------------------------------------------------------+
//| 7. 实现异常恢复机制，在错误发生后自动恢复EA运行                      |
//+------------------------------------------------------------------+
bool AttemptErrorRecovery(int errorCode, string context)
{
   LogDebug("尝试错误恢复: " + context + " 错误代码=" + IntegerToString(errorCode));

   switch(errorCode)
   {
      case ERR_SERVER_BUSY:
      case ERR_BROKER_BUSY:
      case ERR_TOO_FREQUENT_REQUESTS:
      case ERR_TOO_MANY_REQUESTS:
         LogInfo("服务器忙碌，等待重试...");
         Sleep(5000); // 等待5秒
         return true;

      case ERR_NO_CONNECTION:
         LogInfo("连接丢失，等待重连...");
         Sleep(10000); // 等待10秒
         return true;

      case ERR_PRICE_CHANGED:
      case ERR_REQUOTE:
         LogInfo("价格变化，刷新价格后重试");
         RefreshRates();
         Sleep(1000);
         return true;

      case ERR_INVALID_PRICE:
      case ERR_OFF_QUOTES:
         LogInfo("价格无效，等待新价格");
         Sleep(2000);
         RefreshRates();
         return true;

      case ERR_TRADE_TIMEOUT:
         LogInfo("交易超时，稍后重试");
         Sleep(3000);
         return true;

      case ERR_MARKET_CLOSED:
         LogInfo("市场关闭，暂停交易");
         return false; // 不能恢复，需要等待市场开放

      case ERR_NOT_ENOUGH_MONEY:
         LogError("资金不足，停止交易");
         g_IsTradeAllowed = false;
         return false;

      case ERR_TRADE_DISABLED:
         LogError("交易被禁用");
         g_IsTradeAllowed = false;
         return false;

      default:
         LogError("未知错误，无法自动恢复");
         return false;
   }
}

//+------------------------------------------------------------------+
//| 记录系统状态                                                        |
//+------------------------------------------------------------------+
void LogSystemStatus()
{
   string status = "系统状态: ";
   status += "初始化=" + (string)g_IsInitialized;
   status += " | 交易允许=" + (string)g_IsTradeAllowed;
   status += " | 订单数=" + IntegerToString(CountOrdersByMagic());
   status += " | 网格级别=" + IntegerToString(g_CurrentGridLevel);
   status += " | 总盈亏=" + DoubleToString(g_TotalProfit, 2);

   LogDebug(status);
}

//+------------------------------------------------------------------+
//| 记录市场信息                                                        |
//+------------------------------------------------------------------+
void LogMarketInfo()
{
   string market = "市场信息: ";
   market += "Bid=" + DoubleToString(Bid, Digits);
   market += " | Ask=" + DoubleToString(Ask, Digits);
   market += " | 点差=" + DoubleToString((Ask-Bid)/Point, 1);
   market += " | 时间=" + TimeToString(TimeCurrent());

   LogDebug(market);
}

//+------------------------------------------------------------------+
//| 网格交易增强 (Grid Trading Enhancement)                           |
//+------------------------------------------------------------------+

// 网格级别信息结构
struct GridLevel
{
   int level;
   double entryPrice;
   double gridStep;
   int ticket;
   datetime openTime;
};

// 全局网格管理变量
GridLevel g_GridLevels[];
double g_CurrentVolatility = 0;
datetime g_LastVolatilityUpdate = 0;
int g_GridResetCount = 0;

//+------------------------------------------------------------------+
//| 1. 实现DynamicGridStep()函数，根据市场波动率动态调整加仓间距        |
//+------------------------------------------------------------------+
double DynamicGridStep(int level = 0)
{
   if(!EnableDynamicGrid)
   {
      return GridStep; // 使用固定网格间距
   }

   // 计算当前市场波动率
   double volatility = CalculateVolatility();

   // 基础网格间距
   double baseStep = GridStep;

   // 根据波动率调整间距
   double adjustedStep = baseStep * (1.0 + volatility * VolatilityMultiplier);

   // 4. 支持不等间距网格设置，允许不同级别使用不同间距
   // 随着级别增加，间距逐渐增大
   double levelMultiplier = 1.0 + (level * 0.2); // 每级增加20%
   adjustedStep *= levelMultiplier;

   // 限制在最小和最大间距范围内
   if(adjustedStep < MinGridStep) adjustedStep = MinGridStep;
   if(adjustedStep > MaxGridStep) adjustedStep = MaxGridStep;

   LogDebug("动态网格间距: Level=" + IntegerToString(level) +
           " Volatility=" + DoubleToString(volatility, 4) +
           " Step=" + DoubleToString(adjustedStep, 1));

   return adjustedStep;
}

//+------------------------------------------------------------------+
//| 计算市场波动率                                                      |
//+------------------------------------------------------------------+
double CalculateVolatility()
{
   // 如果距离上次更新不到1分钟，使用缓存值
   if(TimeCurrent() - g_LastVolatilityUpdate < 60)
   {
      return g_CurrentVolatility;
   }

   // 使用ATR(14)计算波动率
   double atr = iATR(Symbol(), PERIOD_CURRENT, 14, 0);

   if(atr <= 0)
   {
      // 如果ATR无效，使用简单的价格范围计算
      double high = iHigh(Symbol(), PERIOD_CURRENT, 0);
      double low = iLow(Symbol(), PERIOD_CURRENT, 0);
      atr = high - low;
   }

   // 标准化波动率（相对于平均价格）
   double avgPrice = (Bid + Ask) / 2;
   double volatility = (atr / avgPrice) * 100; // 转换为百分比

   g_CurrentVolatility = volatility;
   g_LastVolatilityUpdate = TimeCurrent();

   return volatility;
}

//+------------------------------------------------------------------+
//| 2. 实现GridLevelManager()函数，管理多级网格的层次结构              |
//+------------------------------------------------------------------+
void GridLevelManager()
{
   // 更新网格级别信息
   UpdateGridLevels();

   // 5. 添加网格密度控制，防止网格过于密集
   if(!CheckGridDensity())
   {
      LogInfo("网格密度过高，暂停新增网格");
      return;
   }

   // 检查是否需要网格重置
   if(EnableGridReset && ShouldResetGrid())
   {
      ResetGrid();
      return;
   }

   // 管理现有网格级别
   ManageExistingGrids();
}

//+------------------------------------------------------------------+
//| 更新网格级别信息                                                    |
//+------------------------------------------------------------------+
void UpdateGridLevels()
{
   // 清空现有网格信息
   ArrayResize(g_GridLevels, 0);

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         // 解析网格级别（从订单注释中）
         string comment = OrderComment();
         int level = ExtractLevelFromComment(comment);

         // 添加到网格级别数组
         int size = ArraySize(g_GridLevels);
         ArrayResize(g_GridLevels, size + 1);

         g_GridLevels[size].level = level;
         g_GridLevels[size].entryPrice = OrderOpenPrice();
         g_GridLevels[size].ticket = OrderTicket();
         g_GridLevels[size].openTime = OrderOpenTime();
         g_GridLevels[size].gridStep = DynamicGridStep(level);
      }
   }

   // 按级别排序
   SortGridLevels();
}

//+------------------------------------------------------------------+
//| 从订单注释中提取级别                                                |
//+------------------------------------------------------------------+
int ExtractLevelFromComment(string comment)
{
   int pos = StringFind(comment, "L");
   if(pos >= 0)
   {
      string levelStr = StringSubstr(comment, pos + 1);
      return (int)StringToInteger(levelStr);
   }
   return 0;
}

//+------------------------------------------------------------------+
//| 排序网格级别                                                        |
//+------------------------------------------------------------------+
void SortGridLevels()
{
   int size = ArraySize(g_GridLevels);

   for(int i = 0; i < size - 1; i++)
   {
      for(int j = i + 1; j < size; j++)
      {
         if(g_GridLevels[i].level > g_GridLevels[j].level)
         {
            GridLevel temp = g_GridLevels[i];
            g_GridLevels[i] = g_GridLevels[j];
            g_GridLevels[j] = temp;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 3. 实现OptimalEntry()函数，判断最佳的网格入场点                    |
//+------------------------------------------------------------------+
bool OptimalEntry(double &entryPrice, int &orderType, int &gridLevel)
{
   if(ArraySize(g_GridLevels) == 0)
   {
      return false; // 没有现有网格
   }

   // 找到最后一个网格级别
   int lastLevel = g_GridLevels[ArraySize(g_GridLevels) - 1].level;
   double lastPrice = g_GridLevels[ArraySize(g_GridLevels) - 1].entryPrice;
   int lastOrderType = GetOrderTypeFromPrice(lastPrice);

   // 计算动态网格间距
   double dynamicStep = DynamicGridStep(lastLevel + 1);

   // 检查当前价格是否达到下一个网格点
   double currentPrice = (lastOrderType == OP_BUY) ? Bid : Ask;
   double distance = MathAbs(currentPrice - lastPrice) / Point;

   if(distance >= dynamicStep)
   {
      gridLevel = lastLevel + 1;
      orderType = lastOrderType; // 继续同一方向
      entryPrice = currentPrice;

      LogDebug("最佳入场点: Level=" + IntegerToString(gridLevel) +
              " Price=" + DoubleToString(entryPrice, Digits) +
              " Distance=" + DoubleToString(distance, 1));

      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 根据价格判断订单类型                                                |
//+------------------------------------------------------------------+
int GetOrderTypeFromPrice(double price)
{
   // 简单判断：如果价格接近Ask，可能是买单；接近Bid，可能是卖单
   if(MathAbs(price - Ask) < MathAbs(price - Bid))
   {
      return OP_BUY;
   }
   else
   {
      return OP_SELL;
   }
}

//+------------------------------------------------------------------+
//| 5. 添加网格密度控制，防止网格过于密集                               |
//+------------------------------------------------------------------+
bool CheckGridDensity()
{
   int gridCount = ArraySize(g_GridLevels);

   if(gridCount < 2)
   {
      return true; // 网格数量少，密度正常
   }

   // 计算平均网格间距
   double totalDistance = 0;
   int validDistances = 0;

   for(int i = 1; i < gridCount; i++)
   {
      double distance = MathAbs(g_GridLevels[i].entryPrice - g_GridLevels[i-1].entryPrice) / Point;
      if(distance > 0)
      {
         totalDistance += distance;
         validDistances++;
      }
   }

   if(validDistances == 0)
   {
      return true;
   }

   double avgDistance = totalDistance / validDistances;

   // 如果平均间距小于最小网格间距的80%，认为过于密集
   if(avgDistance < MinGridStep * 0.8)
   {
      LogInfo("网格密度过高: 平均间距=" + DoubleToString(avgDistance, 1) +
             " 最小要求=" + DoubleToString(MinGridStep * 0.8, 1));
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| 6. 实现网格重置功能，在特定条件下重新构建网格                        |
//+------------------------------------------------------------------+
bool ShouldResetGrid()
{
   // 检查总盈利是否达到重置阈值
   double totalProfit = CalculateTotalProfit();

   if(totalProfit >= GridResetProfit)
   {
      LogInfo("达到网格重置盈利阈值: " + DoubleToString(totalProfit, 2));
      return true;
   }

   // 检查网格是否过于复杂
   int gridCount = ArraySize(g_GridLevels);
   if(gridCount >= MaxOrders * 0.8) // 达到最大订单数的80%
   {
      LogInfo("网格复杂度过高，建议重置: " + IntegerToString(gridCount) + " 个级别");
      return true;
   }

   // 检查最大回撤
   double drawdown = CalculateGridDrawdown();
   if(drawdown > AccountBalance() * 0.1) // 回撤超过余额的10%
   {
      LogInfo("网格回撤过大，建议重置: " + DoubleToString(drawdown, 2));
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| 计算网格回撤                                                        |
//+------------------------------------------------------------------+
double CalculateGridDrawdown()
{
   double maxProfit = 0;
   double currentProfit = CalculateTotalProfit();

   // 这里可以实现更复杂的回撤计算
   // 简化版本：如果当前是亏损，则回撤等于亏损金额
   if(currentProfit < 0)
   {
      return MathAbs(currentProfit);
   }

   return 0;
}

//+------------------------------------------------------------------+
//| 执行网格重置                                                        |
//+------------------------------------------------------------------+
void ResetGrid()
{
   LogInfo("开始网格重置...");

   // 记录重置前的状态
   double totalProfit = CalculateTotalProfit();
   int orderCount = CountOrdersByMagic();

   // 关闭所有订单
   if(CloseAllOrders())
   {
      LogInfo("网格重置完成: 关闭了 " + IntegerToString(orderCount) + " 个订单，总盈利: " + DoubleToString(totalProfit, 2));

      // 清空网格级别信息
      ArrayResize(g_GridLevels, 0);

      // 重置计数器
      g_CurrentGridLevel = 0;
      g_GridResetCount++;

      // 更新统计
      g_TotalProfit += totalProfit;

      LogInfo("网格重置次数: " + IntegerToString(g_GridResetCount));
   }
   else
   {
      LogError("网格重置失败: 部分订单无法关闭");
   }
}

//+------------------------------------------------------------------+
//| 管理现有网格                                                        |
//+------------------------------------------------------------------+
void ManageExistingGrids()
{
   // 检查是否有网格级别需要调整
   for(int i = 0; i < ArraySize(g_GridLevels); i++)
   {
      // 检查止损止盈设置
      if(OrderSelect(g_GridLevels[i].ticket, SELECT_BY_TICKET))
      {
         // 动态调整止损止盈（如果需要）
         UpdateGridOrderStopLoss(g_GridLevels[i].ticket, g_GridLevels[i].level);
      }
   }
}

//+------------------------------------------------------------------+
//| 更新网格订单的止损                                                  |
//+------------------------------------------------------------------+
void UpdateGridOrderStopLoss(int ticket, int level)
{
   if(!OrderSelect(ticket, SELECT_BY_TICKET))
   {
      return;
   }

   // 根据网格级别动态调整止损
   double currentSL = OrderStopLoss();
   double newSL = CalculateDynamicStopLoss(OrderOpenPrice(), OrderType(), level);

   if(MathAbs(newSL - currentSL) > Point && newSL > 0)
   {
      SetStopLoss(ticket, newSL);
      LogDebug("更新网格订单止损: Ticket=" + IntegerToString(ticket) +
              " Level=" + IntegerToString(level) +
              " NewSL=" + DoubleToString(newSL, Digits));
   }
}

//+------------------------------------------------------------------+
//| 计算动态止损                                                        |
//+------------------------------------------------------------------+
double CalculateDynamicStopLoss(double openPrice, int orderType, int level)
{
   // 根据网格级别调整止损距离
   double baseStopLoss = StopLoss;
   double levelMultiplier = 1.0 + (level * 0.1); // 每级增加10%
   double adjustedStopLoss = baseStopLoss * levelMultiplier;

   double stopLossPrice = 0;

   if(orderType == OP_BUY)
   {
      stopLossPrice = openPrice - adjustedStopLoss * Point;
   }
   else if(orderType == OP_SELL)
   {
      stopLossPrice = openPrice + adjustedStopLoss * Point;
   }

   return NormalizeDouble(stopLossPrice, Digits);
}

//+------------------------------------------------------------------+
//| 7. 优化网格算法，提高网格交易的效率和盈利能力                        |
//+------------------------------------------------------------------+
void OptimizeGridStrategy()
{
   // 分析网格性能
   AnalyzeGridPerformance();

   // 调整网格参数
   AdjustGridParameters();

   // 优化入场时机
   OptimizeEntryTiming();
}

//+------------------------------------------------------------------+
//| 分析网格性能                                                        |
//+------------------------------------------------------------------+
void AnalyzeGridPerformance()
{
   if(ArraySize(g_GridLevels) < 2)
   {
      return;
   }

   double totalProfit = CalculateTotalProfit();
   int profitableGrids = 0;
   int totalGrids = ArraySize(g_GridLevels);

   for(int i = 0; i < totalGrids; i++)
   {
      if(OrderSelect(g_GridLevels[i].ticket, SELECT_BY_TICKET))
      {
         double orderProfit = OrderProfit() + OrderSwap() + OrderCommission();
         if(orderProfit > 0)
         {
            profitableGrids++;
         }
      }
   }

   double profitRate = (double)profitableGrids / totalGrids * 100;

   LogDebug("网格性能分析: 总网格=" + IntegerToString(totalGrids) +
           " 盈利网格=" + IntegerToString(profitableGrids) +
           " 盈利率=" + DoubleToString(profitRate, 1) + "%" +
           " 总盈利=" + DoubleToString(totalProfit, 2));
}

//+------------------------------------------------------------------+
//| 调整网格参数                                                        |
//+------------------------------------------------------------------+
void AdjustGridParameters()
{
   // 根据市场条件动态调整参数
   double volatility = CalculateVolatility();

   // 高波动率时增加网格间距
   if(volatility > 0.5) // 0.5%以上的波动率
   {
      LogDebug("高波动率环境，建议增加网格间距");
   }
   // 低波动率时减少网格间距
   else if(volatility < 0.1) // 0.1%以下的波动率
   {
      LogDebug("低波动率环境，可以减少网格间距");
   }
}

//+------------------------------------------------------------------+
//| 优化入场时机                                                        |
//+------------------------------------------------------------------+
void OptimizeEntryTiming()
{
   // 检查市场趋势
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   double currentPrice = (Bid + Ask) / 2;

   // 趋势判断
   bool upTrend = currentPrice > ma20;
   bool downTrend = currentPrice < ma20;

   if(upTrend)
   {
      LogDebug("检测到上升趋势，优先考虑买入网格");
   }
   else if(downTrend)
   {
      LogDebug("检测到下降趋势，优先考虑卖出网格");
   }
   else
   {
      LogDebug("横盘市场，适合双向网格策略");
   }
}

//+------------------------------------------------------------------+
//| 增强版网格检查函数                                                  |
//+------------------------------------------------------------------+
bool EnhancedCheckGrid(int &gridLevel, int &orderType, double &entryPrice)
{
   // 更新网格管理
   GridLevelManager();

   // 使用优化的入场点判断
   if(OptimalEntry(entryPrice, orderType, gridLevel))
   {
      // 额外的安全检查
      if(ComprehensiveRiskCheck(CalculateLotSize(gridLevel)))
      {
         return true;
      }
      else
      {
         LogInfo("增强网格检查: 风险控制阻止了网格入场");
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| 用户界面和信息显示 (User Interface and Information Display)       |
//+------------------------------------------------------------------+

// UI对象名称前缀
#define UI_PREFIX "Martingale_"

//+------------------------------------------------------------------+
//| 1. 创建DisplayInfo()函数，在图表上显示当前交易信息                  |
//+------------------------------------------------------------------+
void DisplayInfo()
{
   if(!ShowInfoPanel)
   {
      return;
   }

   // 获取当前交易信息
   int orderCount = CountOrdersByMagic();
   double totalProfit = CalculateTotalProfit();
   double accountBalance = AccountBalance();
   double accountEquity = AccountEquity();
   double freeMargin = AccountFreeMargin();

   // 计算显示位置
   int x = 10, y = 20;
   if(InfoPanelCorner == 1 || InfoPanelCorner == 3) x = 200; // 右侧
   if(InfoPanelCorner == 2 || InfoPanelCorner == 3) y = 100; // 下方

   // 创建信息面板背景
   CreateInfoPanel(x, y);

   // 显示基本信息
   string infoText = "";
   infoText += "=== Martingale EA ===\n";
   infoText += "状态: " + (g_IsTradeAllowed ? "运行中" : "已停止") + "\n";
   infoText += "时间: " + TimeToString(TimeCurrent(), TIME_SECONDS) + "\n";
   infoText += "货币对: " + Symbol() + "\n\n";

   // 4. 显示当前持仓状态，包括订单数量和总盈亏
   infoText += "=== 持仓信息 ===\n";
   infoText += "订单数量: " + IntegerToString(orderCount) + "/" + IntegerToString(MaxOrders) + "\n";
   infoText += "网格级别: " + IntegerToString(g_CurrentGridLevel) + "\n";

   // 显示盈亏信息
   color profitColor = (totalProfit >= 0) ? ProfitColor : LossColor;
   infoText += "浮动盈亏: " + DoubleToString(totalProfit, 2) + "\n";
   infoText += "总盈亏: " + DoubleToString(g_TotalProfit, 2) + "\n\n";

   // 显示账户信息
   infoText += "=== 账户信息 ===\n";
   infoText += "余额: " + DoubleToString(accountBalance, 2) + "\n";
   infoText += "净值: " + DoubleToString(accountEquity, 2) + "\n";
   infoText += "可用保证金: " + DoubleToString(freeMargin, 2) + "\n\n";

   // 5. 显示策略参数，如当前手数、加仓级别等
   infoText += "=== 策略参数 ===\n";
   infoText += "初始手数: " + DoubleToString(Lots, 2) + "\n";
   infoText += "加仓倍数: " + DoubleToString(Multiplier, 1) + "\n";
   infoText += "网格间距: " + IntegerToString(GridStep) + " 点\n";
   infoText += "交易方向: " + GetTradeDirectionText() + "\n";

   // 显示动态信息
   if(EnableDynamicGrid)
   {
      double volatility = CalculateVolatility();
      infoText += "市场波动率: " + DoubleToString(volatility, 3) + "%\n";
      infoText += "动态间距: " + DoubleToString(DynamicGridStep(g_CurrentGridLevel), 1) + " 点\n";
   }

   // 创建文本标签
   CreateTextLabel(UI_PREFIX + "InfoPanel", infoText, x + 5, y + 5, InfoTextColor);
}

//+------------------------------------------------------------------+
//| 创建信息面板背景                                                    |
//+------------------------------------------------------------------+
void CreateInfoPanel(int x, int y)
{
   string panelName = UI_PREFIX + "InfoBackground";

   // 删除现有面板
   ObjectDelete(0, panelName);

   // 创建矩形背景
   ObjectCreate(0, panelName, OBJ_RECTANGLE_LABEL, 0, 0, 0);
   ObjectSetInteger(0, panelName, OBJPROP_CORNER, InfoPanelCorner);
   ObjectSetInteger(0, panelName, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, panelName, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, panelName, OBJPROP_XSIZE, 250);
   ObjectSetInteger(0, panelName, OBJPROP_YSIZE, 300);
   ObjectSetInteger(0, panelName, OBJPROP_BGCOLOR, clrBlack);
   ObjectSetInteger(0, panelName, OBJPROP_BORDER_TYPE, BORDER_FLAT);
   ObjectSetInteger(0, panelName, OBJPROP_COLOR, clrGray);
   ObjectSetInteger(0, panelName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, panelName, OBJPROP_WIDTH, 1);
   ObjectSetInteger(0, panelName, OBJPROP_BACK, false);
   ObjectSetInteger(0, panelName, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, panelName, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, panelName, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 创建文本标签                                                        |
//+------------------------------------------------------------------+
void CreateTextLabel(string name, string text, int x, int y, color textColor)
{
   // 删除现有标签
   ObjectDelete(0, name);

   // 创建文本标签
   ObjectCreate(0, name, OBJ_LABEL, 0, 0, 0);
   ObjectSetString(0, name, OBJPROP_TEXT, text);
   ObjectSetInteger(0, name, OBJPROP_CORNER, InfoPanelCorner);
   ObjectSetInteger(0, name, OBJPROP_XDISTANCE, x);
   ObjectSetInteger(0, name, OBJPROP_YDISTANCE, y);
   ObjectSetInteger(0, name, OBJPROP_COLOR, textColor);
   ObjectSetString(0, name, OBJPROP_FONT, "Courier New");
   ObjectSetInteger(0, name, OBJPROP_FONTSIZE, 8);
   ObjectSetInteger(0, name, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, name, OBJPROP_SELECTED, false);
   ObjectSetInteger(0, name, OBJPROP_HIDDEN, true);
}

//+------------------------------------------------------------------+
//| 获取交易方向文本                                                    |
//+------------------------------------------------------------------+
string GetTradeDirectionText()
{
   switch(TradeDirection)
   {
      case 0: return "仅买入";
      case 1: return "仅卖出";
      case 2: return "双向";
      default: return "未知";
   }
}

//+------------------------------------------------------------------+
//| 2. 创建DrawLines()函数，绘制重要的价格线和支撑阻力位                |
//+------------------------------------------------------------------+
void DrawLines()
{
   if(!ShowGridLines)
   {
      return;
   }

   // 清除现有线条
   ClearGridLines();

   // 绘制网格线
   DrawGridLines();

   // 绘制重要价格线
   DrawImportantPriceLines();
}

//+------------------------------------------------------------------+
//| 绘制网格线                                                          |
//+------------------------------------------------------------------+
void DrawGridLines()
{
   if(ArraySize(g_GridLevels) == 0)
   {
      return;
   }

   // 绘制每个网格级别的价格线
   for(int i = 0; i < ArraySize(g_GridLevels); i++)
   {
      string lineName = UI_PREFIX + "GridLine_" + IntegerToString(g_GridLevels[i].level);
      double price = g_GridLevels[i].entryPrice;

      // 创建水平线
      ObjectCreate(0, lineName, OBJ_HLINE, 0, 0, price);
      ObjectSetInteger(0, lineName, OBJPROP_COLOR, GridLineColor);
      ObjectSetInteger(0, lineName, OBJPROP_STYLE, STYLE_DOT);
      ObjectSetInteger(0, lineName, OBJPROP_WIDTH, 1);
      ObjectSetString(0, lineName, OBJPROP_TEXT, "Grid L" + IntegerToString(g_GridLevels[i].level));
      ObjectSetInteger(0, lineName, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, lineName, OBJPROP_HIDDEN, true);
   }
}

//+------------------------------------------------------------------+
//| 绘制重要价格线                                                      |
//+------------------------------------------------------------------+
void DrawImportantPriceLines()
{
   // 绘制移动平均线
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   if(ma20 > 0)
   {
      string maLineName = UI_PREFIX + "MA20_Line";
      ObjectCreate(0, maLineName, OBJ_HLINE, 0, 0, ma20);
      ObjectSetInteger(0, maLineName, OBJPROP_COLOR, clrBlue);
      ObjectSetInteger(0, maLineName, OBJPROP_STYLE, STYLE_DASH);
      ObjectSetInteger(0, maLineName, OBJPROP_WIDTH, 2);
      ObjectSetString(0, maLineName, OBJPROP_TEXT, "MA20: " + DoubleToString(ma20, Digits));
      ObjectSetInteger(0, maLineName, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, maLineName, OBJPROP_HIDDEN, true);
   }

   // 绘制当前Bid/Ask线
   string bidLineName = UI_PREFIX + "Bid_Line";
   string askLineName = UI_PREFIX + "Ask_Line";

   ObjectCreate(0, bidLineName, OBJ_HLINE, 0, 0, Bid);
   ObjectSetInteger(0, bidLineName, OBJPROP_COLOR, clrRed);
   ObjectSetInteger(0, bidLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, bidLineName, OBJPROP_WIDTH, 1);
   ObjectSetString(0, bidLineName, OBJPROP_TEXT, "Bid: " + DoubleToString(Bid, Digits));

   ObjectCreate(0, askLineName, OBJ_HLINE, 0, 0, Ask);
   ObjectSetInteger(0, askLineName, OBJPROP_COLOR, clrBlue);
   ObjectSetInteger(0, askLineName, OBJPROP_STYLE, STYLE_SOLID);
   ObjectSetInteger(0, askLineName, OBJPROP_WIDTH, 1);
   ObjectSetString(0, askLineName, OBJPROP_TEXT, "Ask: " + DoubleToString(Ask, Digits));
}

//+------------------------------------------------------------------+
//| 清除网格线                                                          |
//+------------------------------------------------------------------+
void ClearGridLines()
{
   // 清除所有以UI_PREFIX开头的对象
   for(int i = ObjectsTotal() - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, UI_PREFIX) == 0)
      {
         if(StringFind(objName, "GridLine_") > 0 ||
            StringFind(objName, "MA20_Line") > 0 ||
            StringFind(objName, "Bid_Line") > 0 ||
            StringFind(objName, "Ask_Line") > 0)
         {
            ObjectDelete(0, objName);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 3. 创建ShowStatus()函数，显示EA的运行状态和参数                     |
//+------------------------------------------------------------------+
void ShowStatus()
{
   if(!ShowStatusInfo)
   {
      return;
   }

   // 在图表右上角显示简要状态
   string statusText = "";
   statusText += "Martingale EA | ";
   statusText += (g_IsTradeAllowed ? "运行" : "停止") + " | ";
   statusText += "订单:" + IntegerToString(CountOrdersByMagic()) + " | ";

   double totalProfit = CalculateTotalProfit();
   statusText += "盈亏:" + DoubleToString(totalProfit, 1);

   // 创建状态标签
   string statusName = UI_PREFIX + "StatusBar";
   CreateTextLabel(statusName, statusText, 10, 5, InfoTextColor);
}

//+------------------------------------------------------------------+
//| 6. 实现可配置的界面元素，允许用户自定义显示内容                      |
//+------------------------------------------------------------------+
void UpdateUI()
{
   // 根据用户配置更新界面
   if(ShowInfoPanel)
   {
      DisplayInfo();
   }
   else
   {
      // 隐藏信息面板
      ObjectDelete(0, UI_PREFIX + "InfoBackground");
      ObjectDelete(0, UI_PREFIX + "InfoPanel");
   }

   if(ShowGridLines)
   {
      DrawLines();
   }
   else
   {
      ClearGridLines();
   }

   if(ShowStatusInfo)
   {
      ShowStatus();
   }
   else
   {
      ObjectDelete(0, UI_PREFIX + "StatusBar");
   }

   // 刷新图表
   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 清理所有UI对象                                                      |
//+------------------------------------------------------------------+
void CleanupUI()
{
   // 删除所有UI对象
   for(int i = ObjectsTotal() - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, UI_PREFIX) == 0)
      {
         ObjectDelete(0, objName);
      }
   }

   ChartRedraw(0);
}

//+------------------------------------------------------------------+
//| 动态止损止盈 (Dynamic Stop Loss and Take Profit)                  |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| 1. 实现TrailingStop()移动止损                                      |
//+------------------------------------------------------------------+
void TrailingStop()
{
   if(!EnableTrailingStop)
   {
      return;
   }

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         double openPrice = OrderOpenPrice();
         double currentSL = OrderStopLoss();
         int orderType = OrderType();
         double newSL = 0;

         if(orderType == OP_BUY)
         {
            // 买单移动止损
            double trailPrice = Bid - TrailingStopDistance * Point;
            if(currentSL < trailPrice && trailPrice < Bid)
            {
               newSL = trailPrice;
            }
         }
         else if(orderType == OP_SELL)
         {
            // 卖单移动止损
            double trailPrice = Ask + TrailingStopDistance * Point;
            if((currentSL > trailPrice || currentSL == 0) && trailPrice > Ask)
            {
               newSL = trailPrice;
            }
         }

         if(newSL > 0 && MathAbs(newSL - currentSL) > Point)
         {
            SetStopLoss(tickets[i], newSL);
            LogInfo("移动止损更新: Ticket=" + IntegerToString(tickets[i]) +
                   " NewSL=" + DoubleToString(newSL, Digits));
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 2. 实现PartialTakeProfit()分批止盈                                 |
//+------------------------------------------------------------------+
void PartialTakeProfit()
{
   if(!EnablePartialTP)
   {
      return;
   }

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         double profit = OrderProfit() + OrderSwap() + OrderCommission();
         double lots = OrderLots();

         // 如果盈利达到一定水平，执行分批止盈
         if(profit > 10.0 && lots > MarketInfo(Symbol(), MODE_MINLOT) * 2)
         {
            double partialLots = lots * (PartialTPPercent / 100.0);
            partialLots = NormalizeDouble(partialLots, 2);

            if(partialLots >= MarketInfo(Symbol(), MODE_MINLOT))
            {
               if(CloseOrder(tickets[i], partialLots))
               {
                  LogInfo("分批止盈执行: Ticket=" + IntegerToString(tickets[i]) +
                         " ClosedLots=" + DoubleToString(partialLots, 2) +
                         " Profit=" + DoubleToString(profit, 2));
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 3. 实现DynamicSL()动态止损调整                                     |
//+------------------------------------------------------------------+
void DynamicSL()
{
   if(!UseATRForSL)
   {
      return;
   }

   double atr = iATR(Symbol(), PERIOD_CURRENT, 14, 0);
   if(atr <= 0)
   {
      return;
   }

   int tickets[];
   int count = GetOrderTicketsByMagic(tickets);

   for(int i = 0; i < count; i++)
   {
      if(OrderSelect(tickets[i], SELECT_BY_TICKET))
      {
         double openPrice = OrderOpenPrice();
         int orderType = OrderType();
         double dynamicSLDistance = atr * 2; // ATR的2倍作为止损距离
         double newSL = 0;

         if(orderType == OP_BUY)
         {
            newSL = openPrice - dynamicSLDistance;
         }
         else if(orderType == OP_SELL)
         {
            newSL = openPrice + dynamicSLDistance;
         }

         if(newSL > 0)
         {
            double currentSL = OrderStopLoss();
            if(MathAbs(newSL - currentSL) > Point * 5) // 只有变化超过5点才更新
            {
               SetStopLoss(tickets[i], newSL);
               LogDebug("ATR动态止损更新: Ticket=" + IntegerToString(tickets[i]) +
                       " ATR=" + DoubleToString(atr, Digits) +
                       " NewSL=" + DoubleToString(newSL, Digits));
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 执行所有动态止损止盈策略                                            |
//+------------------------------------------------------------------+
void ExecuteDynamicSLTP()
{
   TrailingStop();
   PartialTakeProfit();
   DynamicSL();
}

//+------------------------------------------------------------------+
//| 资金管理优化 (Money Management Optimization) - Task 10            |
//+------------------------------------------------------------------+
double RiskPercentage()
{
   double balance = AccountBalance();
   double equity = AccountEquity();
   double riskPercent = ((balance - equity) / balance) * 100;
   return riskPercent;
}

bool MaxDrawdownCheck()
{
   double maxDrawdown = AccountBalance() * 0.2; // 20%最大回撤
   double currentDrawdown = AccountBalance() - AccountEquity();
   return (currentDrawdown < maxDrawdown);
}

void EquityCurveAnalysis()
{
   static double lastEquity = 0;
   double currentEquity = AccountEquity();

   if(lastEquity > 0)
   {
      double equityChange = currentEquity - lastEquity;
      LogDebug("资金曲线分析: 净值变化=" + DoubleToString(equityChange, 2));
   }

   lastEquity = currentEquity;
}

//+------------------------------------------------------------------+
//| 市场分析模块 (Market Analysis Module) - Task 11                   |
//+------------------------------------------------------------------+
bool TrendAnalysis()
{
   double ma20 = iMA(Symbol(), PERIOD_CURRENT, 20, 0, MODE_SMA, PRICE_CLOSE, 0);
   double ma50 = iMA(Symbol(), PERIOD_CURRENT, 50, 0, MODE_SMA, PRICE_CLOSE, 0);
   double currentPrice = (Bid + Ask) / 2;

   bool upTrend = (currentPrice > ma20 && ma20 > ma50);
   bool downTrend = (currentPrice < ma20 && ma20 < ma50);

   LogDebug("趋势分析: " + (upTrend ? "上升" : downTrend ? "下降" : "横盘"));
   return upTrend || downTrend;
}

double VolatilityCheck()
{
   return CalculateVolatility();
}

bool OptimalTiming()
{
   // 简单的时机判断
   int hour = Hour();
   return (hour >= 8 && hour <= 18); // 主要交易时间
}

//+------------------------------------------------------------------+
//| 高级统计分析 (Advanced Statistical Analysis) - Task 14            |
//+------------------------------------------------------------------+
double WinRateCalculation()
{
   // 简化的胜率计算
   static int totalTrades = 0;
   static int winningTrades = 0;

   totalTrades++;
   double totalProfit = CalculateTotalProfit();
   if(totalProfit > 0) winningTrades++;

   return totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
}

double ProfitFactorAnalysis()
{
   double totalProfit = CalculateTotalProfit();
   double totalLoss = MathAbs(MathMin(totalProfit, 0));

   return totalLoss > 0 ? MathAbs(totalProfit) / totalLoss : 1.0;
}

void PerformanceMetrics()
{
   double winRate = WinRateCalculation();
   double profitFactor = ProfitFactorAnalysis();

   LogInfo("性能指标: 胜率=" + DoubleToString(winRate, 1) + "% 盈利因子=" + DoubleToString(profitFactor, 2));
}

//+------------------------------------------------------------------+
//| 性能优化 (Performance Optimization) - Task 12                     |
//+------------------------------------------------------------------+
void OptimizePerformance()
{
   // 减少不必要的计算
   static datetime lastOptimization = 0;
   if(TimeCurrent() - lastOptimization < 60) return; // 每分钟优化一次

   // 清理无效对象
   CleanupInvalidObjects();

   // 优化内存使用
   OptimizeMemoryUsage();

   lastOptimization = TimeCurrent();
}

void CleanupInvalidObjects()
{
   // 清理无效的UI对象
   for(int i = ObjectsTotal() - 1; i >= 0; i--)
   {
      string objName = ObjectName(0, i);
      if(StringFind(objName, UI_PREFIX) == 0)
      {
         // 检查对象是否仍然有效
         if(!ObjectGetInteger(0, objName, OBJPROP_TYPE))
         {
            ObjectDelete(0, objName);
         }
      }
   }
}

void OptimizeMemoryUsage()
{
   // 优化数组大小
   if(ArraySize(g_GridLevels) > MaxOrders * 2)
   {
      ArrayResize(g_GridLevels, MaxOrders);
   }
}

//+------------------------------------------------------------------+
//| 多货币对支持 (Multi-Currency Support) - Task 13                   |
//+------------------------------------------------------------------+
void MultiSymbolManager()
{
   // 简化的多货币对管理
   string symbols[] = {"EURUSD", "GBPUSD", "USDJPY"};

   for(int i = 0; i < ArraySize(symbols); i++)
   {
      if(symbols[i] == Symbol())
      {
         LogDebug("当前货币对: " + symbols[i] + " 在支持列表中");
         break;
      }
   }
}

bool CorrelationAnalysis()
{
   // 简化的相关性分析
   return true; // 假设相关性可接受
}

void RiskDiversification()
{
   // 简化的风险分散
   LogDebug("风险分散: 当前仅交易单一货币对");
}

//+------------------------------------------------------------------+
//| 综合执行函数                                                        |
//+------------------------------------------------------------------+
void ExecuteAdvancedFeatures()
{
   // 每分钟执行一次高级功能
   static datetime lastExecution = 0;
   if(TimeCurrent() - lastExecution < 60) return;

   EquityCurveAnalysis();
   TrendAnalysis();
   PerformanceMetrics();
   OptimizePerformance();
   MultiSymbolManager();

   lastExecution = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 辅助函数: 检查交易时间                                              |
//+------------------------------------------------------------------+
bool IsTradeTime()
{
   // 简单的交易时间检查
   int currentHour = Hour();

   // 避免在市场关闭时间交易（周末）
   int dayOfWeek = DayOfWeek();
   if(dayOfWeek == 0 || dayOfWeek == 6) // 周日或周六
   {
      return false;
   }

   // 避免在重要新闻时间交易（可以根据需要调整）
   // 这里只是示例，实际使用时可以添加更复杂的逻辑

   return true;
}

//+------------------------------------------------------------------+
//| 辅助函数: 获取EA状态信息                                            |
//+------------------------------------------------------------------+
string GetEAStatus()
{
   string status = "EA状态: ";

   if(!g_IsInitialized)
      status += "未初始化";
   else if(!g_IsTradeAllowed)
      status += "交易禁用";
   else if(!IsTradeTime())
      status += "非交易时间";
   else
      status += "正常运行";

   return status;
}

//+------------------------------------------------------------------+
//| 程序结束                                                          |
//+------------------------------------------------------------------+
